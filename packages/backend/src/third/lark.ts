/* eslint-disable @typescript-eslint/no-explicit-any */
// lark 接口本身有很多any返回类型，这里还无法完全禁用掉any
import { SheetItemElement, SheetRange } from '@pa/shared/dist/src/lark/sheetItem';
import { ChatInfo, ChatTopNotice, NetworkCode, NetworkResult, User } from '@pa/shared/dist/src/core';
import { UserData, UserIdType, UserInfo } from '@pa/shared/dist/src/lark/userInfo';
import { CreateChatArgs, CreateChatQuery, CreateChatResult } from '@pa/shared/dist/src/lark/createChat';
import { LarkCard, MessageType } from '@pa/shared/dist/src/lark/larkCard';
import { Inject, Injectable } from '@gulux/gulux';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { LarkClient, withUserAccessToken } from '@gulux/gulux/lark';
import fetch from 'node-fetch';
import { ApprovalFormValue, LarkApprovalConfig, LarkApprovalDefineConfig } from '@pa/shared/dist/src/lark/approval';
import _, { chunk, compact, uniq } from 'lodash';
import { add_suffix_ne, wait } from '@pa/shared/dist/src/utils/tools';
import { useInject } from '@edenx/plugin-gulux/runtime';
import { PaAlarmService } from '../utils/alarm';
import UserService from '../service/user';
import { ChatShareLink } from '@pa/shared/dist/src/lark/chat';
import { LOG_CHAT_ID } from '../utils/common';
import MessageService from '../service/message';
import { RedisMethodCache } from '../utils/methodCache';

// 默认用户缓存过期时间，单位秒
const USER_CACHE_EXP = 1;

@Injectable()
export default class LarkService {
  @Inject()
  private logger: BytedLogger;

  @Inject()
  private lark: LarkClient;

  @Inject()
  private messageService: MessageService;

  async searchUser(userName: string, accessToken = ''): Promise<User[] | number> {
    if (!accessToken) {
      accessToken = await useInject(UserService).queryAccessToken();
    }
    const result = await this.lark.request(
      {
        method: 'GET',
        url: '/open-apis/search/v1/user',
        data: {},
        params: { query: userName },
      },
      withUserAccessToken(accessToken),
    );
    this.logger.info(`searchUser ${JSON.stringify(result)}`);
    if (result.code === 0) {
      return this.batchGetUserInfo(
        result.data.users.map((it: any) => it.open_id),
        UserIdType.openId,
      );
    } else {
      return NetworkCode.Error;
    }
  }

  // async searchCalender() {
  //   const result = await this.lark.calendar.calendar.search({
  //     data: {
  //       query: "ByteDance Public Calendar (China's Mainland)",
  //     },
  //   });
  //   if (result.code === 0) {
  //     return result.data?.items[0].calendar_id;
  //   }
  //   return undefined;
  // }

  // async getCalenderEvents(calendarId: string, startTime: string, endTime: string) {
  //   const result = await this.lark.calendar.calendarEvent.list({
  //     params: {
  //       start_time: startTime,
  //       end_time: endTime,
  //     },
  //     path: {
  //       calendar_id: calendarId,
  //     },
  //   });
  async historyMessageList(
    containerId = 'oc_af3f82db604a92a47be4de6108ee2dbf',
    containerIdType = 'chat',
    sort_type: 'ByCreateTimeAsc' | 'ByCreateTimeDesc' = 'ByCreateTimeDesc',
    maxCount = 1000,
    start_time?: string,
    end_time?: string,
  ) {
    let page_token: string | undefined;
    const messageList: any[] = [];
    let hasMore = true;
    let count = 0;
    while (hasMore) {
      const rsp = await this.lark.im.v1.message.list({
        params: {
          container_id_type: containerIdType,
          container_id: containerId,
          sort_type: 'ByCreateTimeDesc',
          page_size: 50,
          ...(start_time ? { start_time } : {}),
          ...(end_time ? { end_time } : {}),
          ...(page_token ? { page_token } : {}),
        },
      });
      if (rsp?.code !== 0 || !rsp.data) {
        break;
      }
      messageList.push(...(rsp?.data?.items ?? []));
      hasMore = rsp?.data?.has_more ?? false;
      page_token = rsp?.data?.page_token;
      count += rsp?.data?.items?.length ?? 0;
      if (count > maxCount) {
        break;
      }
    }
    return messageList;
  }
  //   return result;

  // }

  async batchGetUserInfo(userIds: string[], user_id_type: UserIdType) {
    if (userIds.length <= 0) {
      return [];
    }
    const params = new URLSearchParams();
    params.append('user_id_type', user_id_type);
    userIds.forEach(userId => {
      params.append('user_ids', userId);
    });
    const result = await this.lark.request({
      method: 'GET',
      url: `/open-apis/contact/v3/users/batch?${params.toString()}`,
      data: {},
    });
    if (result.code === 0) {
      return result.data.items;
    } else {
      throw new Error(result.msg);
    }
  }

  @RedisMethodCache('Lark:getUserIdByEmail', USER_CACHE_EXP)
  async getUserIdByEmail(email: string): Promise<UserData | undefined> {
    const result = await this.lark.request({
      method: 'POST',
      url: '/open-apis/user/v4/email2id',
      data: { email },
    });
    if (result.code === 0) {
      result.data.email = email;
      return result.data;
    }
    return undefined;
  }

  /**
   *  特别注意：这里的userId是OpenId，openId是空，如果是直接使用错会消息发送不了，或者拉群失败等等
   */
  async getUserIdByEmails(emails: string[]): Promise<UserData[] | undefined> {
    const res = await this.lark.contact.user.batchGetId({
      data: {
        emails,
      },
    });
    if (res.code === 0) {
      return res.data?.user_list as UserData[];
    }
  }

  async getUserInfoByEmail(email: string): Promise<User | undefined> {
    const id = await this.getUserIdByEmail(email);
    if (id) {
      return this.getUserInfo(id.open_id);
    }
    return undefined;
  }

  async getUserInfoByEmails(emails: string[]): Promise<User[] | undefined> {
    if (emails.length === 0) {
      return [];
    }
    const result = await Promise.all(emails.map((it, idx) => wait(400 * idx).then(() => this.getUserInfoByEmail(it))));
    return compact(result);
  }

  async batchGetUserId(
    userIdType: UserIdType,
    query: {
      emails?: string[];
      mobiles?: string[];
    },
  ): Promise<UserData[]> {
    const result = await this.lark.contact.v3.user.batchGetId({
      data: query,
      params: {
        user_id_type: userIdType as any,
      },
    });
    return (result?.data?.user_list ?? []) as UserData[];
  }

  @RedisMethodCache('Lark:getUserIdByOpenId', USER_CACHE_EXP)
  async getUserIdByOpenId(userIdType: UserIdType, user_id: string) {
    const result = await this.lark.contact.v3.user.get({
      params: {
        user_id_type: userIdType as any,
      },
      path: {
        user_id,
      },
    });
    return result?.data?.user as UserInfo | undefined;
  }

  async checkUserValid(email: string) {
    const res = await this.getUserIdByEmail(add_suffix_ne('@bytedance.com')(email));
    return res?.open_id !== undefined;
  }

  async batchCheckUserValid(emails: string[]) {
    const res = await this.batchGetUserId(UserIdType.openId, {
      emails: emails.map(add_suffix_ne('@bytedance.com')),
    });
    return emails.map(email => ({
      email,
      exists: res.find(s => s.email === add_suffix_ne('@bytedance.com')(email))?.user_id !== undefined,
    }));
  }

  /**
   * copy from @ruoxin
   * @param approvalCode
   */
  async searchApproval(approvalCode: string): Promise<LarkApprovalConfig | undefined> {
    const result = await this.lark.approval.approval.get({
      path: {
        approval_code: approvalCode,
      },
    });
    return result.data;
  }

  async createApprovalConfig(config: LarkApprovalDefineConfig) {
    return await this.lark.approval.v4.approval.create({
      data: config,
    });
  }

  async subscribe(approvalCode: string) {
    return await this.lark.approval.v4.approval.subscribe({
      path: {
        approval_code: approvalCode,
      },
    });
  }

  async unSubscribe(approvalCode: string) {
    return await this.lark.approval.v4.approval.unsubscribe({
      path: {
        approval_code: approvalCode,
      },
    });
  }

  async createApproval(
    approvalCode: string,
    userId: string,
    approvalUserKey: string,
    approverUserId: string,
    formInfo: ApprovalFormValue[],
  ): Promise<any | undefined> {
    const result = await this.lark.approval.instance.create({
      data: {
        approval_code: approvalCode,
        user_id: userId,
        node_approver_user_id_list: [{ key: approvalUserKey, value: [approverUserId] }],
        form: JSON.stringify(formInfo),
      },
    });
    return result.data?.instance_code;
  }

  @RedisMethodCache('Lark:searchUserInfo', USER_CACHE_EXP)
  async searchUserInfo(userIdType: UserIdType, userId: string): Promise<UserInfo | undefined> {
    const result = await this.lark.contact.v3.user.get({
      params: {
        user_id_type: userIdType as any,
      },
      path: {
        user_id: userId,
      },
    });
    this.logger.info(`[searchUserInfo]:${JSON.stringify(result)}`);
    return result?.data?.user as UserInfo | undefined;
  }

  async getUserEmail(openId: string): Promise<string> {
    const result = await this.getUserInfo(openId);
    return result?.email ?? '';
  }

  @RedisMethodCache('Lark:getUserDepartment', USER_CACHE_EXP)
  async getUserDepartment(openId: string): Promise<string> {
    const directoryResult = await this.lark.request({
      method: 'POST',
      url: `/open-apis/directory/v1/employees/mget?employee_id_type=open_id`,
      params: {
        employee_id_type: 'open_id',
      },
      data: {
        employee_ids: [openId],
        required_fields: ['base_info.departments.name', 'base_info.department_id'],
      },
    });
    return directoryResult.data.employees[0].base_info.departments[0].name.default_value ?? ' ';
  }

  @RedisMethodCache('Lark:getUserInfo', USER_CACHE_EXP)
  async getUserInfo(openId: string): Promise<User | undefined> {
    const res = await this.lark.contact.user.get({
      path: {
        user_id: openId,
      },
    });
    return res.data?.user as User;
  }

  async createLarkGroup(query: CreateChatQuery, body: CreateChatArgs): Promise<CreateChatResult> {
    body.bot_id_list = ['cli_9c8628b7b1f1d102'];
    const result = await this.lark.im.v1.chat.create({
      data: body,
      params: query as any,
    });
    this.logger.info(`[createLarkGroup]:${JSON.stringify(result)}`);
    if (result.code === 0 && result.data) {
      return result.data as CreateChatResult;
    }
    throw new Error(result.msg);
  }

  /**
   *
   * @param chatId 7278616719642427393
   * @return OpenChatId oc_abbfe86cbe3c0dbdf8faa93c847e0c1d
   */
  async chatId2OpenChatId(chatId: string): Promise<string> {
    const url = `https://oncall-backend.bytedance.net/api/inf/v1/platform/chat_id_to_open_chat_id?chat_id=${chatId}`;
    const resp = await fetch(url).then(it => it.json());
    return resp.data ?? '';
  }

  async getChatIdShareLink(openChatId: string): Promise<ChatShareLink> {
    const result = await this.lark.im.v1.chat.link({
      path: {
        chat_id: openChatId,
      },
      data: {
        validity_period: 'permanently',
      },
    });
    if (result.code === 0 && result.data) {
      return result.data as ChatShareLink;
    }
    throw new Error(result.msg);
  }

  async addUserToChatGroup(chatId: string, idType: UserIdType, id_list: string[]): Promise<NetworkResult<string>> {
    if (id_list.length <= 0) {
      return { code: NetworkCode.Success, message: 'success' };
    }

    const result = await this.lark.request({
      method: 'POST',
      url: `/open-apis/im/v1/chats/${chatId}/members`,
      data: { id_list },
      params: {
        member_id_type: idType,
        succeed_type: 1,
      },
    });

    if (result.code === 0) {
      return { code: NetworkCode.Success, message: 'success' };
    }
    return { code: NetworkCode.Error, message: result.msg };
  }

  async putTopNotice(chatId: string, chat_top_notice: ChatTopNotice[]) {
    const result = await this.lark.request({
      method: 'POST',
      url: `/open-apis/im/v1/chats/${chatId}/top_notice/put_top_notice`,
      data: { chat_top_notice },
    });
  }

  /**
   * @deprecated 请使用 MessageService.sendMsg 发送消息
   * 发送纯文本
   * @param userIdType
   * @param id
   * @param content
   */
  async sendTextMessage(userIdType: UserIdType, id: string, content: string) {
    const text = {
      text: content,
    };
    await this.sendMessage(userIdType, id, JSON.stringify(text), MessageType.text);
  }

  async replyMessage(content: string, messageId: string, msgType: MessageType) {
    return await this.lark.im.message
      .reply({
        path: {
          message_id: messageId,
        },
        data: {
          content,
          msg_type: msgType,
        },
      })
      .catch(async err => {
        this.logger.error(`[sendMessage]: err ${JSON.stringify(err)}`);
        try {
          const e: Error = err instanceof Error ? err : new Error(JSON.stringify(err));
          if (![230053, 230002].includes(err.code)) {
            // 本地不告警/用户屏蔽机器人不告警/机器人不在群里不告警
            useInject(PaAlarmService).reportReqError(e);
          }
        } catch (e) {}
        return err;
      });
  }

  async updateCard(content: string, messageId: string) {
    return await this.lark.im.message
      .patch({
        path: {
          message_id: messageId,
        },
        data: {
          content,
        },
      })
      .catch(async err => {
        this.logger.error(`[sendMessage]: err ${JSON.stringify(err)}`);
        try {
          const e: Error = err instanceof Error ? err : new Error(JSON.stringify(err));
          if (![230053, 230002].includes(err.code)) {
            // 本地不告警/用户屏蔽机器人不告警/机器人不在群里不告警
            useInject(PaAlarmService).reportReqError(e);
          }
        } catch (e) {}
        return err;
      });
  }

  async inviteUsers2Group(ids: string[], chatId: string) {
    const res = await this.lark.im.chatMembers.create({
      path: { chat_id: chatId },
      data: {
        id_list: ids,
      },
    });
    return res;
  }

  /**
   * @deprecated 请使用 MessageService.sendMsg 发送消息
   * @param userIdType
   * @param id
   * @param content
   * @param msgType
   */
  async sendMessage(userIdType: UserIdType, id: string, content: string, msgType: MessageType) {
    return this.messageService.sendLegacyMsg(userIdType, id, content, msgType);
  }

  async makeUrgent(message_id: string, urgent_user_ids: string[]) {
    return await this.lark.im.message.urgentApp({
      path: {
        message_id,
      },
      params: {
        user_id_type: 'open_id',
      },
      data: {
        user_id_list: urgent_user_ids,
      },
    });
  }

  async deleteMessages(message_ids: string[]) {
    for (const id of message_ids) {
      const result = await this.lark.im.message.delete({
        path: {
          message_id: id,
        },
      });
      this.logger.debug(`[deleteMessage]:${JSON.stringify(result)}`);
    }
  }

  // 搜索对用户或机器人可见的群列表
  // /open-apis/im/v1/chats/search
  async searchChats(queryInfo: string): Promise<string> {
    const result = await this.lark.im.v1.chat.search({
      params: {
        query: queryInfo,
        page_size: 1,
      },
    });
    this.logger.info(`[searchChats]:${JSON.stringify(result.data?.items)}`);
    return result.data?.items?.shift()?.chat_id ?? '';
  }

  // 将群组的 LarkId 转化为 OpenId
  async larkChatI2OpenChatId(larkChatId: string): Promise<string> {
    const result = await this.lark.request({
      method: 'POST',
      url: '/open-apis/exchange/v3/cid2ocid',
      data: {
        chat_id: larkChatId,
      },
    });
    this.logger.info(`[larkChatI2OpenChatId] larkChatId: ${larkChatId}, openChatId: ${result.open_chat_id}`);
    if (result.code === 0) {
      return result.open_chat_id;
    } else {
      return '';
    }
  }

  async clearTable(spreadsheetToken: string, sheetId: string) {
    return await this.lark.request({
      method: 'POST',
      url: `/open-apis/sheets/v3/spreadsheets/${spreadsheetToken}/sheets/${sheetId}/values/batch_clear`,
      data: {
        ranges: [sheetId],
      },
    });
  }

  async insertItemForTable(
    spreadsheetToken: string,
    sheetId: string,
    valueRange: SheetItemElement[][][],
  ): Promise<any> {
    return await this.lark.request({
      method: 'POST',
      url: `/open-apis/sheets/v3/spreadsheets/${spreadsheetToken}/sheets/${sheetId}/values/${sheetId}/insert`,
      data: {
        values: valueRange,
      },
    });
  }

  // 更新群信息：https://open.feishu.cn/document/server-docs/group/chat/update-2?appId=cli_9c8628b7b1f1d102
  async changeGroupChatInfo(chatId: string, groupName: string): Promise<any> {
    return await this.lark.im.v1.chat.update({
      path: {
        chat_id: chatId,
      },
      data: {
        name: groupName,
      },
    });
  }

  // ------- 以下是原来 lark V2 -------
  async uploadImage(img: Buffer): Promise<string | undefined> {
    return (
      await this.lark.im.image.create({
        data: {
          image_type: 'message',
          image: img,
        },
      })
    )?.image_key;
  }

  // duplicate
  // async sendTextMessage(userIdType: UserIdType, id: string, content: string)

  /**
   * @deprecated 请使用 MessageService.sendMsg 发送消息
   * @param userIdType
   * @param id
   * @param card
   */
  async sendCardMessage(userIdType: UserIdType, id: string, card: LarkCard) {
    return await this.sendMessage(userIdType, id, JSON.stringify(card), MessageType.interactive);
  }

  /**
   * @deprecated 请使用 MessageService.sendMsg 发送消息
   * @param userIdType
   * @param id
   * @param card
   * @param urgent
   */
  async sendCardAndUrgent(userIdType: UserIdType, id: string, card: LarkCard, urgent = true) {
    const sendMsgResp = await this.sendCardMessage(userIdType, id, card);
    if (!urgent) {
      return sendMsgResp;
    }

    const urgentUsers = sendMsgResp.data?.mentions?.map((it: any) => it.id);
    const messageId = sendMsgResp.data?.message_id;
    if (messageId && urgentUsers && urgentUsers.length > 0) {
      const urgentResp = await this.makeUrgent(messageId, urgentUsers);
      console.log(`[sendCardAndUrgent] Urgent result: ${JSON.stringify(urgentResp)}`);
    }
    return sendMsgResp;
  }

  async checkBotInCharGroup(chatId: string): Promise<boolean> {
    const checkResult = await this.lark.im.chatMembers.isInChat({
      path: { chat_id: chatId },
    });
    return Boolean(checkResult.code === 0 && checkResult.data?.is_in_chat);
  }

  async searchUserInfoByEmail(email: string): Promise<User | undefined> {
    const userInfo = await this.getUserIdByEmail(email);
    if (userInfo) {
      const result = await this.lark.contact.user.get({
        params: { user_id_type: UserIdType.openId },
        path: { user_id: userInfo.open_id },
      });
      if (result.code === 0 && result.data) {
        return {
          user_id: userInfo.user_id,
          email: result.data.user?.email ? result.data.user?.email : '',
          avatar: result.data.user?.avatar,
          name: result.data.user?.name ? result.data.user?.name : '',
          open_id: userInfo.open_id,
        };
      }
    }
    return;
  }

  async searchUserInfoBatch(emailList: string[]): Promise<User[]> {
    const ret = chunk(emailList, 50).map(emails =>
      this.batchGetUserId(UserIdType.openId, {
        emails: emails.map(add_suffix_ne('@bytedance.com')),
      })
        .then(it => compact(it.map(u => u.open_id ?? u.user_id)))
        .then(it => this.batchGetUserInfo(it, UserIdType.openId))
        .then(
          it =>
            it.map((u: any) => ({
              user_id: u?.user_id ?? '',
              email: u.email ?? '',
              avatar: u.avatar,
              name: u.name,
              open_id: u.open_id ?? '',
            })) ?? [],
        ),
    );
    return await Promise.all(ret).then(it => it.flat());
  }

  async queryReadUsers(
    message_id: string,
    user_id_type?: 'user_id' | 'open_id' | 'union_id',
  ): Promise<{ user_id: string; timestamp: string }[]> {
    let has_more = false;
    let page_token: string | undefined;
    const ret: { user_id: string; timestamp: string }[] = [];
    do {
      const resp = await this.lark.im.message.readUsers({
        path: {
          message_id,
        },
        params: page_token
          ? {
              user_id_type: user_id_type ?? 'open_id',
              page_size: 100,
              page_token,
            }
          : {
              user_id_type: user_id_type ?? 'open_id',
              page_size: 100,
            },
      });
      ret.push(
        ...resp.data!.items!.map(it => ({
          user_id: it.user_id!,
          timestamp: it.timestamp!,
        })),
      );
      has_more = Boolean(resp.data?.has_more);
      page_token = resp.data?.page_token;
    } while (has_more);
    return ret;
  }

  async queryChatUsers(chat_id: string) {
    let has_more = false;
    let page_token: string | undefined;
    const ret: string[] = [];
    do {
      const resp = await this.lark.im.chatMembers.get({
        path: {
          chat_id,
        },
        params: {
          member_id_type: 'open_id',
          page_size: 100,
          page_token,
        },
      });
      ret.push(...resp.data!.items!.map(it => it.member_id!));
      has_more = Boolean(resp.data?.has_more);
      page_token = resp.data?.page_token;
    } while (has_more);
    return ret;
  }

  async searchChatInfo(keyWords: string): Promise<ChatInfo[] | undefined> {
    const result = await this.lark.im.chat.search({
      params: {
        user_id_type: UserIdType.openId,
        query: keyWords,
        page_size: 50,
      },
    });
    return result.data?.items;
  }

  async inviteBotToChat(chat_id: string, bot_id: string, token?: string) {
    const res = await this.lark.im.chatMembers.create(
      {
        path: { chat_id },
        params: { member_id_type: 'app_id' },
        data: {
          id_list: [bot_id],
        },
      },
      token ? withUserAccessToken(token) : undefined,
    );
    console.log(res);
    return res;
  }

  async Ocid2Cid(open_chat_id: string): Promise<string> {
    const result = await this.lark.request({
      method: 'POST',
      url: '/open-apis/exchange/v3/ocid2cid',
      data: {
        open_chat_id,
      },
    });
    return result.chat_id;
  }

  async hasDocPermission(
    doc_token: string,
    type: 'doc' | 'sheet' | 'bitable' | 'mindnote' | 'file' | 'wiki' | 'docx' | 'minutes' | 'slides',
    action: 'view' | 'edit' | 'share' | 'comment' | 'export',
  ): Promise<boolean> {
    const ret = await this.lark.drive.v1.permissionMember.auth({
      params: {
        type,
        action,
      },
      path: {
        token: doc_token,
      },
    });
    return Boolean(ret.data?.auth_result);
  }

  /**
   * 快速发送消息到 test api 群
   * @param msg
   */
  async quickLog(msg: string) {
    return await this.sendMessage(
      UserIdType.chatId,
      LOG_CHAT_ID,
      JSON.stringify({
        text: msg,
      }),
      MessageType.text,
    );
  }

  async getJSApiTicket(accessToken = ''): Promise<string | NetworkCode> {
    if (!accessToken) {
      accessToken = await useInject(UserService).queryAccessToken();
    }
    const result = await this.lark.request(
      {
        method: 'POST',
        url: '/open-apis/jssdk/ticket/get',
      },
      withUserAccessToken(accessToken),
    );
    this.logger.info(`${JSON.stringify(result)}`);
    if (result.code === 0) {
      return result.data.ticket;
    } else {
      return '';
    }
  }

  async addChatGroupTabs(
    tabs: Array<{
      tab_name: string;
      tab_type: 'message' | 'doc_list' | 'doc' | 'pin' | 'meeting_minute' | 'chat_announcement' | 'url' | 'file';
      tab_content?: {
        url?: string;
        doc?: string;
        meeting_minute?: string;
      };
      tab_config?: {
        icon_key?: string;
        is_built_in?: boolean;
      };
    }>,
    chatId: string,
  ): Promise<boolean> {
    const allTabName = tabs.map(value => value.tab_name);
    const result = await this.lark.im.v1.chatTab.create({
      data: { chat_tabs: tabs },
      path: { chat_id: chatId },
    });
    return result?.data?.chat_tabs?.filter(value => allTabName.includes(value.tab_name ?? '')).length === tabs.length;
  }

  async listChatGroupTabs(chatId: string) {
    const result = await this.lark.im.v1.chatTab.listTabs({
      path: { chat_id: chatId },
    });
    if (result && result.data && result.data.chat_tabs) {
      return result.data.chat_tabs as { tab_name: string }[];
    }
    return [];
  }

  async bitableAppend(appToken: string, tableId: string, record: Record<string, any>[]) {
    const ret = await this.lark.bitable.appTableRecord.batchCreate({
      data: {
        records: record.map(it => ({
          fields: it,
        })),
      },
      path: {
        app_token: appToken,
        table_id: tableId,
      },
    });
    return ret;
  }

  async spreadsheetsRange(spreadsheetToken: string, sheetId: string, range: string) {
    const ret = await this.lark.request({
      method: 'GET',
      url: `/open-apis/sheets/v2/spreadsheets/${spreadsheetToken}/values/${sheetId}!${range}?user_id_type=open_id`,
      data: {},
      params: {},
    });
    if (ret.code !== 0) {
      throw new Error(ret.msg);
    }
    return ret.data as SheetRange;
  }

  async spreadsheetsAppend(spreadsheetToken: string, sheetId: string, range: string, values: any[][]) {
    const ret = await this.lark.request({
      method: 'POST',
      url: `/open-apis/sheets/v2/spreadsheets/${spreadsheetToken}/values_append`,
      data: {
        valueRange: {
          range: `${sheetId}!${range}`,
          values,
        },
      },
      params: {},
    });
    if (ret.code !== 0) {
      throw new Error(ret.msg);
    }
    return ret.data;
  }

  async deleteChatGroup(chatId: string) {
    return await this.lark.im.v1.chat.delete({
      path: { chat_id: chatId },
    });
  }

  /**
   * 创建带截止时间的飞书任务
   * @param summary 任务标题（必填）
   * @param dueTime 截止时间戳（ms，必填）
   * @param description 任务描述（必填）
   * @param assigneeOpenId 负责人open_id（必填）
   * @param accessToken 访问凭证（可选，默认从UserService获取）
   * @param followerOpenIds 关注人open_id列表（可选）
   * @returns 任务创建结果（包含任务ID等信息）
   */
  async createTask(
    summary: string,
    dueTime: number,
    description: string,
    assigneeOpenId: string,
    accessToken = '',
    followerOpenIds?: string[],
  ): Promise<NetworkResult<any>> {
    if (!summary) {
      throw new Error('任务标题不能为空');
    }
    if (!dueTime) {
      throw new Error('截止时间不能为空');
    }
    if (!description) {
      throw new Error('任务描述不能为空');
    }
    if (!assigneeOpenId) {
      throw new Error('负责人open_id不能为空');
    }

    const members: any[] = [];
    if (assigneeOpenId) {
      members.push({
        id: assigneeOpenId,
        type: 'user',
        role: 'assignee',
      });
    }
    if (followerOpenIds?.length) {
      followerOpenIds.forEach(id => {
        members.push({
          id,
          type: 'user',
          role: 'follower',
        });
      });
    }

    const requestBody: any = {
      summary,
      due: { timestamp: dueTime },
      ...(description && { description }),
      ...(members.length > 0 && { members }),
    };

    // 获取访问凭证
    if (!accessToken) {
      accessToken = await useInject(UserService).queryAccessToken();
      this.logger.info(accessToken);
    }

    const result = await this.lark.request(
      {
        method: 'POST',
        url: '/open-apis/task/v2/tasks',
        data: requestBody,
      },
      withUserAccessToken(accessToken),
    );

    if (result.code === 0) {
      return { code: NetworkCode.Success, data: result.data, message: 'success' };
    } else {
      this.logger.error(`创建任务失败: ${result.msg}`, {
        summary,
        dueTime,
        errorCode: result.code,
      });
      return { code: NetworkCode.Error, message: result.msg };
    }
  }
}
/* eslint-enable @typescript-eslint/no-explicit-any */
