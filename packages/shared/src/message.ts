import { LarkCard, MessageType } from './lark/larkCard';

// 一级分类
export enum MsgCategory {
  Release = '发版平台',
  Acceptance = '验收平台',
  Experiment = '实验管理',
  Quality = '质量平台',
  DevOps = '工程效能',
  Unknown = '未分类',
}

export enum MsgType {
  DirectChat = '飞书私聊',
  GroupChat = '飞书群聊',
  Site = '站内信',
  Email = '邮件',
}

export enum MsgStrategy {
  Timer = '定时触发',
  Event = '事件触发',
  Manual = '手动触发',
  Auto = '自动触发',
}

export interface MsgTemplateExtraData {}

export interface MsgTemplate {
  name: string; // 消息分类描述, 例如"封版提醒上车消息"
  type: MsgType; // 消息类型：私聊还是群聊
  category: MsgCategory; // 消息一级业务分类,尽量使用枚举
  subCategory: string; // 消息二级业务分类,尽量使用枚举
  strategy: MsgStrategy; // 消息触发策略
  msgContent: LarkCard | string; // 消息内容
  urgent?: boolean; // 是否加急，默认为false
  larkType?: MessageType; // 飞书消息类型，默认为interactive
  extra?: MsgTemplateExtraData;
}

export interface SubscribableMsgTemplate extends MsgTemplate {
  subscribeId: number; // 订阅ID，用于订阅消息的唯一标识
  subscriberList: string[];
}

export interface SendMsgRecord extends MsgTemplate {
  createTs: number;
  receiverList: string[];
  messageId: string;
  hasRead: number;
}

export function fillMsgTemplate(template: MsgTemplate) {
  template.urgent = template.urgent || false;
  template.larkType = template.larkType || MessageType.interactive;
  return template;
}

/**
 * 创建堆栈分析结果的消息模板
 * @param stackTraceAnalysis 堆栈分析结果字符串
 * @returns MsgTemplate 对象
 */
export function createStackTraceAnalysisMsgTemplate(stackTraceAnalysis: string): MsgTemplate {
  return {
    name: '堆栈分析结果',
    type: MsgType.GroupChat,
    category: MsgCategory.Quality,
    subCategory: '崩溃分析',
    strategy: MsgStrategy.Auto,
    msgContent: stackTraceAnalysis,
    urgent: false,
    larkType: MessageType.text,
  };
}
