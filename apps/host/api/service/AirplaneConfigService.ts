import { Inject, Injectable } from '@gulux/gulux';
import { AirplaneConfig, ConfigType, VERSION_CONFIG, VersionConfigKeys } from '@shared/aircraftConfiguration';
import { AirplaneConfigTable } from '../model/AirplaneConfigModel';
import { ModelType } from '@gulux/gulux/typegoose';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import { pick } from 'lodash';
import { PlatformType, ChatInfo, User } from '@pa/shared/dist/src/core';

@Injectable()
export default class AirplaneConfigService {
  @Inject(AirplaneConfigTable)
  private airplaneConfigModel: ModelType<AirplaneConfigTable>;

  @Inject()
  logger: BytedLogger;

  async updateConfig(item: AirplaneConfig) {
    const query = { businessId: item.businessId, name: item.name } as AirplaneConfig;
    const config = VERSION_CONFIG[item.name];
    if (config.requestPlatform) {
      query.platform = item.platform;
    }
    if (Array.isArray(item.data)) {
      item.data = item.data.filter(value => value);
    }
    const update = { ...item };
    const options = { upsert: true, new: true, useFindAndModify: false };
    const result = this.airplaneConfigModel.findOneAndUpdate(query, update, options);
    return result.map(value => pick(value, ['businessId', 'platform', 'name', 'configType', 'data']));
  }

  async updateConfigList(list: AirplaneConfig[]) {
    const upsertPromises = list.map(item => this.updateConfig(item));
    return Promise.all(upsertPromises);
  }

  async queryConfig(businessId: string) {
    const result = await this.airplaneConfigModel
      .where({
        businessId,
      })
      .find()
      .exec();
    this.logger.info(JSON.stringify(result));
    if (result) {
      return result.map(value => pick(value, ['businessId', 'platform', 'name', 'configType', 'data']));
    } else {
      return [];
    }
  }

  async queryConfigItem(
    businessId: string,
    configName: VersionConfigKeys,
    platform: PlatformType | undefined = undefined,
  ): Promise<User | ChatInfo | User[] | ChatInfo[] | any | undefined> {
    const result = await this.airplaneConfigModel
      .where({
        businessId,
        name: configName,
        platform,
      })
      .findOne()
      .exec();
    this.logger.info(JSON.stringify(result));

    if (!result) {
      return;
    }

    if (result.configType === ConfigType.chat) {
      return result.data as ChatInfo;
    }
    if (result.configType === ConfigType.chat_array) {
      return result.data as ChatInfo[];
    }
    if (result.configType === ConfigType.user) {
      return result.data as User;
    }
    if (result.configType === ConfigType.user_array) {
      return result.data as User[];
    }
    return result.data;
  }
}
