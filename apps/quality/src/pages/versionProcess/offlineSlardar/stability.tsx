import React, { useEffect, useRef, useState } from 'react';
import { Col, Divider, Radio, Row, Select, Space, TreeSelect } from 'antd';
import dayjs from 'dayjs';
import { querySlardarInfo } from '@api/slardar';
import { SlardarInfoForGraph } from '@/pages/versionProcess/offlineSlardar/shared';
import StabilityIssueList from '@/pages/versionProcess/offlineSlardar/stabilityIssueList';
import { useModel } from '@edenx/runtime/model';
import AppSettingModule from '@/model/appSettingModel';
import { SlardarPlatformType } from '@pa/shared/dist/src/appSettings/appSettings';
import minMax from 'dayjs/plugin/minMax';
import UserSettingModule from '@/model/userSettingModel';
import { useNavigate, useSearchParams } from '@edenx/runtime/router';
import { getAppVersion, getVersionIntegrationTime } from '@api/version';
import { useCustomSearchParams } from '@/utils/customSearchParams';
import VersionSettingModule from '@/model/versionSettingModel';
import { VersionStageChecklistStatus } from '@shared/releasePlatform/versionStage';
import { DeviceLevel } from '@shared/common';
import { PlatformType } from '@pa/shared/dist/src/core';
import OfflineVersionSelector, { TreeDataItem } from '@/component/VersionSelector/update_offline_index';
import { GroupName, SelectedName } from '@shared/utils/offlineslardar';

function startTime(publishTime: number) {
  return dayjs.unix(publishTime);
}

function endTime(publishTime: number, days: number) {
  dayjs.extend(minMax);
  return dayjs.min(startTime(publishTime).add(days, 'd'), dayjs()) || dayjs();
}

export interface StabilityProps {
  deviceLevel: DeviceLevel;
  versionChecklistStatus?: VersionStageChecklistStatus[];
  groupName: GroupName | null;
}

const Stability: React.FC<StabilityProps> = ({ deviceLevel, groupName, versionChecklistStatus }) => {
  // 版本列表
  // const [versions, setVersions] = useState<string[]>([]);
  const [treeData, setTreeData] = useState<TreeDataItem[]>([]);
  // 将版本和阶段合并为一个选择状态，例如: "25.1.0::after_freeze"
  const [selection, setSelection] = useState<string | undefined>();
  // // 已经选择的“版本”
  // const [selected, setSelected] = useState<string | undefined>(undefined);
  // 所有的这么多 Slardar 指标
  const [slardarMetricInfo, setSlardarMetricInfo] = useState<SlardarInfoForGraph[]>([]);
  const [appSettingState] = useModel(AppSettingModule);
  const [versionSettingState] = useModel(VersionSettingModule);
  const [userSettingState] = useModel(UserSettingModule);
  const navigate = useNavigate();
  const [apiStartTime, setApiStartTime] = useState<number | undefined>();
  const [searchParams, setCustomSearchParam] = useCustomSearchParams(
    useSearchParams(),
    appSettingState,
    versionSettingState,
  );
  const hasInit = useRef(false);
  // 从组合状态中解析出版本和阶段
  const getVersionFromSelection = (sel: string | undefined) => sel?.split('::')[0];
  const getStageFromSelection = (sel: string | undefined): SelectedName | undefined =>
    sel?.split('::')[1] as SelectedName;
  const currentVersion = getVersionFromSelection(selection);
  const currentStage = getStageFromSelection(selection);

  useEffect(() => {
    if (!hasInit.current) {
      return;
    }
    setCustomSearchParam('selected', selection);
  }, [selection]);

  const updateSlardarInfo = async () => {
    const resp = await querySlardarInfo({
      data: { aid: appSettingState.info.businessInfo.aid, platform: appSettingState.info.platform.toString() },
    });
    return resp.InfoList.map(it => ({
      metricName: it.Id,
      displayName: it.DisplayName,
      displayPriority: it.DisplayPriority,
    }));
  };
  useEffect(() => {
    const fetchInitialData = async () => {
      const platform =
        appSettingState.info.platform === PlatformType.iOS ? SlardarPlatformType.iOS : SlardarPlatformType.Android;

      const [versionResponse, slardarInfo] = await Promise.all([
        getAppVersion({ data: { aid: appSettingState.info.businessInfo.aid, platform } }),
        updateSlardarInfo(),
      ]);

      setSlardarMetricInfo(slardarInfo);

      if (versionResponse.code === 200 && versionResponse.data && versionResponse.data.length > 0) {
        const versionList = versionResponse.data;
        const newTreeData = versionList.map(version => {
          // 定义统一的黑色样式
          const style = { color: 'black', fontWeight: 500 };
          return {
            // 为父节点（版本号）应用黑色样式
            title: <span style={style}>{version}</span>,
            value: version,
            disabled: true,
            children: [
              {
                // 子节点可以保持默认颜色，或者也使用该样式
                title: `${version}-封板前`,
                value: `${version}::${SelectedName.BeforeIntegration}`,
              },
              {
                title: `${version}-封板后`,
                value: `${version}::${SelectedName.AfterIntegration}`,
              },
            ],
          };
        });
        setTreeData(newTreeData);
        // setVersions(versionList);

        // const paramSelected = searchParams.get('selected');
        // const paramBest = searchParams.get('best');
        const paramSelection = searchParams.get('selection');
        if (paramSelection) {
          const paramVersion = getVersionFromSelection(paramSelection);
          if (paramVersion && versionList.includes(paramVersion)) {
            setSelection(paramSelection);
            hasInit.current = true;
            return; // 成功设置后提前返回
          }
        }

        if (versionList.length > 0) {
          // 默认选择最新版本的“封板后”
          const latestVersion = versionList[0];
          setSelection(`${latestVersion}::${SelectedName.AfterIntegration}`);
        }
      } else {
        setTreeData([]);
        // setVersions([]);
      }
      hasInit.current = true;
    };

    fetchInitialData();
  }, [appSettingState.info, userSettingState.info]);

  useEffect(() => {
    const fetchStartTime = async () => {
      setApiStartTime(undefined); // 请求开始，先清空状态
      if (currentVersion && currentStage) {
        try {
          const res: any = await getVersionIntegrationTime({
            data: {
              aid: appSettingState.info.businessInfo.aid,
              platform:
                appSettingState.info.platform === PlatformType.Android
                  ? SlardarPlatformType.Android
                  : SlardarPlatformType.iOS,
              version: currentVersion,
              stage: currentStage,
            },
          });
          if (res.data !== null) {
            // 成功，设置时间戳，data 可能是 number 或 null，null 转成 undefined
            setApiStartTime(res.data ?? undefined);
          } else {
            // 失败，打印错误信息，状态设为 undefined
            console.error('获取开始时间失败:', res ?? '未知错误');
            setApiStartTime(undefined);
          }
        } catch (error) {
          // 网络或其他异常，打印错误，状态设为 undefined
          console.error('请求开始时间异常:', error);
          setApiStartTime(undefined);
        }
      }
    };
    fetchStartTime();
  }, [currentVersion, currentStage, appSettingState.info.businessInfo.aid, appSettingState.info.platform]);

  const onVersionChange = (value: undefined | string, label: React.ReactNode[], extra: { title: string }) => {
    setSelection(value);
    // 这里可以根据需要处理 title，比如传递给其他组件
    console.log('Selected title:', extra.title);
  };
  const buildCrashIssueList = () => {
    if (!selection || !currentVersion) {
      return <>请选择一个版本！</>;
    }
    // 等待起始时间接口返回
    if (apiStartTime === undefined) {
      return <>正在获取版本起始时间...</>;
    }

    let start_time: number, end_time: number;
    const integrationTimeDayjs = apiStartTime;

    if (selection.includes(SelectedName.BeforeIntegration)) {
      // 封板前：封板时间点 - 14天 ~ 封板时间点
      start_time = endTime(integrationTimeDayjs, -14).unix();
      // TODO 这周过渡 下周改回来 就是原来的startTime(integrationTimeDayjs).unix();
      end_time = endTime(integrationTimeDayjs, 14).unix();
    } else {
      // 封板后：封板真正结束时间点 ~ 封板真正结束时间点 + 7天
      start_time = startTime(integrationTimeDayjs).unix();
      end_time = endTime(integrationTimeDayjs, 10).unix();
    }
    console.log('start_time:', start_time);
    console.log('end_time:', end_time);
    return (
      <StabilityIssueList
        version={currentVersion}
        baseVersionCode={'0'}
        start_time={start_time}
        end_time={end_time}
        deviceLevel={deviceLevel}
        stage={currentStage!}
      />
    );
  };
  return (
    <>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Space size={'middle'}>
              选择版本：
              <OfflineVersionSelector treeData={treeData} onChange={setSelection} value={selection!} isSingle={true} />
            </Space>
          </div>
        </Col>
      </Row>
      <Divider />
      {buildCrashIssueList()}
    </>
  );
};
export default Stability;
